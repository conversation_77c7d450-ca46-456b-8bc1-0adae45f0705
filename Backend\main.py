from fastapi import <PERSON><PERSON><PERSON>, HTTPException, File, UploadFile, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, Union
from dotenv import load_dotenv
from ingest import DocumentIngester
from query import QueryEngine
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
import socket
import os
import logging
from aws_utils import extract_text_from_image_bytes, analyze_image_with_bedrock
from openrouter_utils import analyze_image_with_openrouter
from image_analysis.api import router as image_analysis_router

import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# Create logger instance
logger = logging.getLogger(__name__)

load_dotenv()

app = FastAPI(
    title="RAG API",
    description="Retrieval-Augmented Generation API using LangChain and AWS services",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the image analysis router
app.include_router(image_analysis_router)

# mcp = FastApiMCP(app)
# mcp.mount()

doc_ingester = DocumentIngester()
query_engine = None

class AdvancedQueryRequest(BaseModel):
    question: str
    retrieval_config: Optional[Dict[str, Any]] = None

class IngestResponse(BaseModel):
    status: str
    message: str
    chunks: Union[int, str]

class ImageQueryRequest(BaseModel):
    retrieval_config: Optional[Dict[str, Any]] = None

class DiagramGraphQueryRequest(BaseModel):
    graph_data: Dict[str, Any]
    question: str

@app.get("/")
async def root():
    return {"message": "Welcome to RAG API"}

@app.post("/ingest", response_model=IngestResponse)
async def ingest_documents():
    try:
        logger.info("Ingestion endpoint called, starting document processing")
        result = doc_ingester.process_s3_documents()
        return result
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    return {"status": "healthy"}

def get_query_engine():
    global query_engine
    if query_engine is None:
        # Force reload vector store to ensure we're using the latest data
        query_engine = QueryEngine()
        # Initialize vector store explicitly
        if hasattr(query_engine, 'vector_store') and query_engine.vector_store is None:
            from vectorstore_utils import load_vectorstore
            query_engine.vector_store = load_vectorstore()
            
        # Fix LLM initialization if needed
        if hasattr(query_engine, 'llm'):
            from langchain_aws import ChatBedrock
            try:
                # Use direct model ID instead of profile ARN which is causing errors
                model_id = "apac.amazon.nova-lite-v1:0"
                logger.info(f"Initializing Bedrock LLM with model: {model_id}")
                
                query_engine.llm = ChatBedrock(
                    model_id=model_id,
                    region_name=os.getenv("AWS_REGION"),
                    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                    model_kwargs={"max_tokens": 1500, "temperature": 0.4}
                )
                logger.info(f"Successfully initialized Bedrock LLM with model: {model_id}")
            except Exception as e:
                logger.error(f"Failed to initialize Bedrock LLM: {e}")
                # Create a simple fallback LLM that returns a formatted response
                from langchain.llms.fake import FakeListLLM
                
                # Create a response that uses the retrieved documents
                def create_response(docs):
                    if not docs:
                        return "No relevant information found."
                    
                    content = "\n\n".join([doc.page_content for doc in docs[:3]])
                    return f"Based on the retrieved documents, here's how to grant user EKS access:\n\n{content}"
                
                query_engine.llm = FakeListLLM(responses=[create_response])
                logger.warning("Using fallback LLM due to Bedrock initialization error")
    return query_engine

@app.post("/query/advanced")
async def advanced_query_endpoint(
    request: AdvancedQueryRequest
):
    """
    Advanced query endpoint.
    """
    try:
        logger.info(f"Advanced query endpoint called with question: '{request.question[:50]}...' (truncated)")
        
        engine = get_query_engine()
        result = engine.query_advanced(
            question=request.question,
            retrieval_config=request.retrieval_config
        )
        
        # Log query success with some metrics
        sources_count = len(result.get("sources", []))
        logger.info(f"Query successful. Found {sources_count} sources. Query type: {result.get('query_type', 'unknown')}")
        
        return result
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Advanced query error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        logger.error(f"Query parameters: question='{request.question[:50]}...' (truncated)")
        raise HTTPException(status_code=500, detail=f"Query failed: {e}")

@app.post("/query/image")
async def image_query_endpoint(file: UploadFile = File(...), request: ImageQueryRequest = None):
    """
    Accepts an image upload, extracts text using Tesseract, and runs retrieval on the extracted text.
    """
    if not file:
        raise HTTPException(status_code=400, detail="No file uploaded")
        
    try:
        # Validate file type
        content_type = file.content_type
        if not content_type or not content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {content_type}. Please upload an image.")
            
        image_bytes = await file.read()
        if not image_bytes:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
            
        # Check image size
        if len(image_bytes) > 20 * 1024 * 1024:  # 20MB limit
            raise HTTPException(status_code=400, detail=f"Image size ({len(image_bytes) / (1024 * 1024):.2f}MB) exceeds the 20MB limit. Please use a smaller image.")
        
        # Try to extract text from the image
        extracted_text = extract_text_from_image_bytes(image_bytes)
        
        # If no text was extracted, return a helpful message
        if not extracted_text.strip():
            return {
                "answer": "No text could be extracted from the image. Try using the /analyze/image or /analyze/image/openrouter endpoints to analyze the image contents instead.", 
                "images": [], 
                "sources": [],
                "extracted_text": ""
            }
            
        # Get the query engine and run the query
        engine = get_query_engine()
        result = engine.query_advanced(
            question=extracted_text,
            retrieval_config=request.retrieval_config if request else None
        )
        
        # Add the extracted text to the result
        result["extracted_text"] = extracted_text
        return result
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Image query error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        logger.error(f"File info: name='{file.filename}', content_type='{file.content_type}'")
        raise HTTPException(status_code=500, detail=f"Image query failed: {e}")

@app.post("/analyze/image")
async def image_analysis_endpoint(file: UploadFile = File(...)):
    """
    Accepts an image upload and analyzes it using AWS Bedrock vision model.
    This endpoint is specifically for analyzing architecture diagrams or screenshots with errors.
    """
    if not file:
        raise HTTPException(status_code=400, detail="No file uploaded")
        
    try:
        # Validate file type
        content_type = file.content_type
        if not content_type or not content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {content_type}. Please upload an image.")
            
        image_bytes = await file.read()
        if not image_bytes:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # First try to extract text with OCR
        extracted_text = extract_text_from_image_bytes(image_bytes)
        
        # Check if AWS credentials are configured
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        aws_region = os.getenv('AWS_REGION')
        
        if not aws_access_key or not aws_secret_key or not aws_region:
            return {
                "status": "error",
                "error": "AWS credentials are not configured. Please set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_REGION environment variables.",
                "extracted_text": extracted_text,
                "analysis": "Image analysis with Bedrock failed due to missing AWS credentials."
            }
        
        # Then analyze the image with Bedrock vision model
        analysis_result = analyze_image_with_bedrock(image_bytes)
        
        # Combine results
        result = {
            "extracted_text": extracted_text,
            "analysis": analysis_result.get("analysis", "Failed to analyze image"),
            "status": analysis_result.get("status", "error"),
            "model_id": analysis_result.get("model_id", "unknown")
        }
        
        # If there was an error with the vision model, include it in the response
        if analysis_result.get("status") == "error":
            result["error"] = analysis_result.get("error", "Unknown error")
        
        return result
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Image analysis error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        logger.error(f"File info: name='{file.filename}', content_type='{file.content_type}'")
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {e}")

@app.post("/analyze/image/openrouter")
async def openrouter_image_analysis_endpoint(file: UploadFile = File(...), prompt: Optional[str] = None):
    """
    Accepts an image upload and analyzes it using OpenRouter API with Mistral Small 3.2 24B vision model.
    This endpoint is specifically for analyzing architecture diagrams or screenshots with errors.
    """
    if not file:
        raise HTTPException(status_code=400, detail="No file uploaded")
        
    try:
        # Validate file type
        content_type = file.content_type
        if not content_type or not content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {content_type}. Please upload an image.")
            
        image_bytes = await file.read()
        if not image_bytes:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # First try to extract text with OCR
        extracted_text = extract_text_from_image_bytes(image_bytes)
        
        # Check if OpenRouter API key is configured
        openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        
        if not openrouter_api_key:
            return {
                "status": "error",
                "error": "OpenRouter API key is not configured. Please set OPENROUTER_API_KEY environment variable.",
                "extracted_text": extracted_text,
                "analysis": "Image analysis with OpenRouter failed due to missing API key."
            }
        
        # Then analyze the image with OpenRouter API
        analysis_result = analyze_image_with_openrouter(image_bytes, prompt)
        
        # Combine results
        result = {
            "extracted_text": extracted_text,
            "analysis": analysis_result.get("analysis", "Failed to analyze image"),
            "status": analysis_result.get("status", "error"),
            "model_id": analysis_result.get("model_id", "unknown")
        }
        
        # If there was an error with the vision model, include it in the response
        if analysis_result.get("status") == "error":
            result["error"] = analysis_result.get("error", "Unknown error")
        
        return result
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"OpenRouter image analysis error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        logger.error(f"File info: name='{file.filename}', content_type='{file.content_type}'")
        logger.error(f"Prompt parameter: {prompt if prompt else 'None'}")
        raise HTTPException(status_code=500, detail=f"OpenRouter image analysis failed: {e}")

@app.post("/query/diagram-graph")
async def diagram_graph_query_endpoint(request: DiagramGraphQueryRequest):
    """
    Query a diagram graph to answer questions about its structure.
    """
    try:
        logger.info(f"Diagram graph query endpoint called with question: '{request.question[:50]}...' (truncated)")
        
        engine = get_query_engine()
        result = engine.query_diagram_graph(
            graph_data=request.graph_data,
            question=request.question
        )
        
        return result
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Diagram graph query error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        raise HTTPException(status_code=500, detail=f"Diagram graph query failed: {e}")

def find_available_port(start_port: int = 8888, max_attempts: int = 10) -> int:
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    raise RuntimeError(f"Could not find an available port after {max_attempts} attempts")

if __name__ == "__main__":
    port = find_available_port()
    uvicorn.run(app, host="0.0.0.0", port=port)