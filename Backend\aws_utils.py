# aws_utils.py - Unstructured.io integration with IAM role support
import boto3
import os
from typing import List, Optional, Dict, Any
import tempfile
import re
import base64
from io import BytesIO
from PIL import Image
import pytesseract
from botocore.exceptions import NoCredentialsError, ClientError
import logging
import json

logger = logging.getLogger(__name__)

def create_aws_session(region_name: str = None) -> boto3.Session:
    """
    Create AWS session using static credentials from environment variables only.

    Args:
        region_name: AWS region name. If None, uses AWS_REGION env var or default.

    Returns:
        boto3.Session: Configured AWS session

    Raises:
        NoCredentialsError: If no valid credentials are found
    """
    region = region_name or os.getenv('AWS_REGION', 'us-east-1')
    access_key = os.getenv('AWS_ACCESS_KEY_ID')
    secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')

    if access_key and secret_key:
        session = boto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
        # Test the session
        try:
            sts_client = session.client('sts')
            identity = sts_client.get_caller_identity()
            logger.info(f"AWS authentication successful using environment variables. Identity: {identity.get('Arn', 'Unknown')}")
        except Exception as e:
            logger.error(f"Failed to validate AWS credentials: {e}")
            raise NoCredentialsError("Invalid AWS credentials or unable to connect to AWS.")
        return session
    else:
        logger.error("AWS_ACCESS_KEY_ID and/or AWS_SECRET_ACCESS_KEY not set in environment variables.")
        raise NoCredentialsError("AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY must be set in environment variables.")

def get_aws_client(service_name: str, region_name: str = None):
    """
    Get AWS service client using static credentials from environment variables only.

    Args:
        service_name: AWS service name (e.g., 's3', 'bedrock-runtime')
        region_name: AWS region name

    Returns:
        AWS service client
    """
    session = create_aws_session(region_name)
    return session.client(service_name)

# Define function to convert elements to text format
def elements_to_text_format(elements):
    """Convert elements to a formatted text representation"""
    if not elements:
        return ""
    
    result = []
    for element in elements:
        if hasattr(element, "text"):
            result.append(element.text)
    
    return "\n\n".join(result)

def simple_clean_whitespace(text):
    """Simple function to clean extra whitespace"""
    if not text:
        return text
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)
    # Trim leading/trailing whitespace
    return text.strip()

class AWSClient:
    def __init__(self, region_name: str = None):
        """
        Initialize AWS client using static credentials from environment variables only.

        Args:
            region_name: AWS region name. If None, uses AWS_REGION env var.
        """
        self.region = region_name or os.getenv('AWS_REGION', 'us-east-1')
        try:
            # Create AWS session using static credentials
            self.session = create_aws_session(self.region)
            self.s3_client = self.session.client('s3')
            logger.info("AWSClient initialized successfully with static credentials from environment variables")
        except Exception as e:
            logger.error(f"Failed to initialize AWSClient: {e}")
            raise
        self._init_unstructured()

    def _init_unstructured(self):
        # No API key needed for local unstructured processing
        pass

    def test_connection(self) -> dict:
        bucket_name = os.getenv('S3_BUCKET_NAME')
        self.s3_client.head_bucket(Bucket=bucket_name)
        response = self.s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
        has_objects = 'Contents' in response
        return {
            "status": "success", 
            "bucket": bucket_name,
            "accessible": True,
            "has_objects": has_objects,
            "parser_status": "unstructured_initialized"
        }

    def list_s3_files(self, bucket_name: str) -> List[str]:
        response = self.s3_client.list_objects_v2(Bucket=bucket_name)
        if 'Contents' not in response:
            return []
        return [obj['Key'] for obj in response['Contents']]

    def read_s3_file(self, bucket_name: str, file_key: str) -> Optional[Dict[str, Any]]:
        """Read and parse file from S3 bucket.
        Returns a dictionary with 'text' content and 'images' list."""
        try:
            response = self.s3_client.get_object(Bucket=bucket_name, Key=file_key)
            content_bytes = response['Body'].read()
            if not content_bytes:
                return {"text": "", "images": []}
            
            head_response = self.s3_client.head_object(Bucket=bucket_name, Key=file_key)
            content_type = head_response.get('ContentType', 'unknown')
            file_extension = os.path.splitext(file_key)[1].lower()
            
            logger.info(f"Processing file: {file_key} (type: {content_type}, ext: {file_extension})")
            
            if file_extension == '.pdf' or 'pdf' in content_type.lower():
                return self._parse_pdf_with_unstructured(content_bytes, file_key)
            elif file_extension == '.docx' or 'officedocument.wordprocessingml.document' in content_type.lower():
                return self._parse_docx_file(content_bytes, file_key)
            else:
                text_content = self._parse_text_file(content_bytes)
                return {"text": text_content, "images": []}
                
        except Exception as e:
            logger.error(f"Error reading S3 file {file_key}: {e}")
            return {"text": "", "images": []}

    def _parse_pdf_with_unstructured(self, content_bytes: bytes, source_filename: str) -> Dict[str, Any]:
        """Parse PDF file using Unstructured.io, including image extraction."""
        try:
            from unstructured.partition.pdf import partition_pdf
            import fitz  # PyMuPDF for image extraction
        except ImportError as e:
            logger.error(f"Required libraries not installed: {e}")
            return {"text": "", "images": []}
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(content_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Use unstructured for text extraction
            elements = partition_pdf(
                filename=temp_file_path,
                strategy="fast",
                infer_table_structure=True,
                metadata_filename=source_filename
            )
            
            # Process text elements
            text_elements = []
            for element in elements:
                if hasattr(element, "text") and element.text:
                    element.text = simple_clean_whitespace(element.text)
                    text_elements.append(element)
            
            # Convert text elements to markdown
            markdown_text = elements_to_text_format(text_elements)
            
            # Extract images using PyMuPDF
            images_data = []
            try:
                pdf_document = fitz.open(temp_file_path)
                
                for page_num in range(len(pdf_document)):
                    page = pdf_document[page_num]
                    image_list = page.get_images(full=True)
                    
                    for img_index, img in enumerate(image_list):
                        try:
                            # Get image data
                            xref = img[0]
                            pix = fitz.Pixmap(pdf_document, xref)
                            
                            # Skip if image is too small (likely noise)
                            if pix.width < 50 or pix.height < 50:
                                pix = None
                                continue
                            
                            # Convert to PNG format
                            if pix.n - pix.alpha < 4:  # GRAY or RGB
                                img_data = pix.tobytes("png")
                            else:  # CMYK: convert to RGB first
                                pix1 = fitz.Pixmap(fitz.csRGB, pix)
                                img_data = pix1.tobytes("png")
                                pix1 = None
                            
                            # Convert to base64
                            img_str = base64.b64encode(img_data).decode('utf-8')
                            
                            # Create image metadata
                            img_metadata = {
                                "id": f"{os.path.basename(source_filename)}_page_{page_num + 1}_img_{img_index}",
                                "base64": img_str,
                                "format": "PNG",
                                "metadata": {
                                    "source": source_filename,
                                    "page_number": page_num + 1,
                                    "width": pix.width,
                                    "height": pix.height,
                                    "caption": self._generate_image_caption(source_filename, page_num + 1)
                                }
                            }
                            images_data.append(img_metadata)
                            
                            pix = None  # Free memory
                            
                        except Exception as e:
                            logger.error(f"Error processing image {img_index} on page {page_num + 1}: {e}")
                            continue
                
                pdf_document.close()
                
            except Exception as e:
                logger.error(f"Error extracting images from PDF: {e}")
            
            logger.info(f"Extracted {len(images_data)} images from PDF: {source_filename}")
            
            return {
                "text": markdown_text,
                "images": images_data
            }
            
        except Exception as e:
            logger.error(f"Error parsing PDF with Unstructured: {e}")
            return {"text": "", "images": []}
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    def _parse_text_file(self, content_bytes: bytes) -> str:
        """Parse a plain text file."""
        try:
            text_content = content_bytes.decode('utf-8')
            return text_content
        except UnicodeDecodeError:
            # Try different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    text_content = content_bytes.decode(encoding)
                    return text_content
                except UnicodeDecodeError:
                    continue
            # If all fail, use errors='ignore'
            return content_bytes.decode('utf-8', errors='ignore')

    def _parse_docx_file(self, content_bytes: bytes, source_filename: str) -> Dict[str, Any]:
        """Parse DOCX file using python-docx for both text and images."""
        try:
            from docx import Document
            from docx.document import Document as DocumentType
            from docx.oxml.table import CT_Tbl
            from docx.oxml.text.paragraph import CT_P
            from docx.table import _Cell, Table
            from docx.text.paragraph import Paragraph
        except ImportError as e:
            logger.error(f"python-docx not installed: {e}")
            return {"text": "", "images": []}
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(content_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Open document
            doc = Document(temp_file_path)
            
            # Extract text content
            text_content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(simple_clean_whitespace(paragraph.text))
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(simple_clean_whitespace(cell.text))
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            markdown_text = "\n\n".join(text_content)
            
            # Extract images
            images_data = []
            try:
                # Get all relationships that are images
                for rel in doc.part.rels.values():
                    if "image" in rel.reltype:
                        try:
                            # Get image data
                            image_part = rel.target_part
                            image_data = image_part.blob
                            
                            # Convert to base64
                            img_str = base64.b64encode(image_data).decode('utf-8')
                            
                            # Determine format from content type
                            content_type = getattr(image_part, 'content_type', 'image/png')
                            img_format = content_type.split('/')[-1].upper()
                            if img_format == 'JPEG':
                                img_format = 'JPG'
                            
                            # Create image metadata
                            img_metadata = {
                                "id": f"{os.path.basename(source_filename)}_img_{len(images_data)}",
                                "base64": img_str,
                                "format": img_format,
                                "metadata": {
                                    "source": source_filename,
                                    "caption": self._generate_image_caption(source_filename)
                                }
                            }
                            images_data.append(img_metadata)
                            
                        except Exception as e:
                            logger.error(f"Error processing image from DOCX: {e}")
                            continue
                            
            except Exception as e:
                logger.error(f"Error extracting images from DOCX: {e}")
            
            logger.info(f"Extracted {len(images_data)} images from DOCX: {source_filename}")
            
            return {
                "text": markdown_text,
                "images": images_data
            }
            
        except Exception as e:
            logger.error(f"Error parsing DOCX: {e}")
            return {"text": "", "images": []}
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    def _generate_image_caption(self, filename: str, page_number: int = None) -> str:
        """Generate a descriptive caption for an image based on filename and context."""
        filename_lower = filename.lower()
        
        # Check for specific keywords in filename
        if any(keyword in filename_lower for keyword in ['architect', 'diagram', 'design', 'flow', 'chart']):
            caption = "Architecture diagram"
        elif any(keyword in filename_lower for keyword in ['screenshot', 'screen', 'ui', 'interface']):
            caption = "Screenshot"
        elif any(keyword in filename_lower for keyword in ['graph', 'plot', 'chart', 'metric']):
            caption = "Chart or graph"
        elif any(keyword in filename_lower for keyword in ['logo', 'brand', 'icon']):
            caption = "Logo or icon"
        else:
            caption = "Image"
        
        if page_number:
            caption += f" from page {page_number}"
            
        return caption

def extract_text_from_image_bytes(image_bytes: bytes) -> str:
    """
    Extract text from image bytes using Tesseract OCR.
    Args:
        image_bytes: Image file as bytes
    Returns:
        Extracted text as a string
    """
    try:
        image = Image.open(BytesIO(image_bytes))
        text = pytesseract.image_to_string(image)
        return text
    except Exception as e:
        logger.error(f"Tesseract OCR failed: {e}")
        return ""

def analyze_image_with_bedrock(image_bytes: bytes) -> Dict[str, Any]:
    """
    Analyze image using AWS Bedrock vision model.
    Args:
        image_bytes: Image file as bytes
    Returns:
        Dictionary containing analysis results
    """
    try:
        # Check image size (API may have limits)
        if len(image_bytes) > 20 * 1024 * 1024:  # 20MB limit
            return {
                "status": "error",
                "error": f"Image size ({len(image_bytes) / (1024 * 1024):.2f}MB) exceeds the 20MB limit",
                "analysis": "Image is too large for processing. Please use a smaller image."
            }
            
        # Try to resize the image if it's larger than 5MB (to avoid base64 encoding issues)
        if len(image_bytes) > 5 * 1024 * 1024:
            try:
                from PIL import Image
                from io import BytesIO
                
                # Open image and resize while maintaining aspect ratio
                with Image.open(BytesIO(image_bytes)) as img:
                    # Calculate new dimensions (maintaining aspect ratio)
                    max_size = 1600  # Maximum dimension in pixels
                    ratio = min(max_size / img.width, max_size / img.height)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    
                    # Resize image
                    img = img.resize(new_size, Image.LANCZOS)
                    
                    # Save to BytesIO
                    buffer = BytesIO()
                    img.save(buffer, format="JPEG", quality=85)
                    image_bytes = buffer.getvalue()
                    
                    logger.info(f"Resized image from {len(image_bytes) / (1024 * 1024):.2f}MB to {len(image_bytes) / (1024 * 1024):.2f}MB")
            except Exception as e:
                logger.warning(f"Failed to resize large image: {e}")
                # Continue with original image
        
        # Convert image to base64
        base64_image = base64.b64encode(image_bytes).decode('utf-8')
        
        # Verify AWS credentials are set
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        aws_region = os.getenv('AWS_REGION')
        
        if not aws_access_key or not aws_secret_key or not aws_region:
            return {
                "status": "error",
                "error": "AWS credentials are not configured. Please set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_REGION environment variables.",
                "analysis": "Image analysis with Bedrock failed due to missing AWS credentials."
            }
        
        # Create Bedrock client
        bedrock_runtime = boto3.client(
            service_name='bedrock-runtime',
            region_name=aws_region,
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        )
        
        # Check if vision model is available, try different models in order of preference
        model_id = os.getenv('BEDROCK_VISION_MODEL_ID', None)
        
        # If no model is specified in env vars, try these models in order
        if not model_id:
            # List of models to try, in order of preference
            models_to_try = [
                'anthropic.claude-3-sonnet-20240229-v1:0',
                'anthropic.claude-3-haiku-20240307-v1:0',
                'meta.llama3-8b-vision-v1:0',
                'amazon.titan-image-generator-v1:0'
            ]
            
            # Use the first available model
            for model in models_to_try:
                try:
                    # Test if we can access this model
                    bedrock_runtime.get_model_invocation_logging_configuration(modelId=model)
                    model_id = model
                    logger.info(f"Using Bedrock model: {model_id}")
                    break
                except Exception as e:
                    logger.warning(f"Cannot access model {model}: {e}")
                    continue
            
            # If no models are available, use the default
            if not model_id:
                model_id = 'anthropic.claude-3-sonnet-20240229-v1:0'
                logger.warning(f"No available models found, defaulting to {model_id}")
        
        # Prepare prompt for different model types
        if 'anthropic' in model_id:
            # Claude format
            prompt = f"""
            Human: <image>
            Please analyze this image in detail. If it's an architecture diagram, explain the components and their relationships. 
            If it's a screenshot containing an error, explain what the error is and suggest possible solutions.
            Provide a comprehensive analysis of what you see in the image.
            """
            
            payload = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 1500,
                "temperature": 0.2,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image", "source": {"type": "base64", "media_type": "image/jpeg", "data": base64_image}}
                        ]
                    }
                ]
            }
        elif 'meta' in model_id and 'vision' in model_id.lower():
            # Llama format
            prompt = "Please analyze this image in detail. If it's an architecture diagram, explain the components and their relationships. If it's a screenshot containing an error, explain what the error is and suggest possible solutions."
            
            payload = {
                "prompt": prompt,
                "max_gen_len": 1024,
                "temperature": 0.2,
                "image_data": [{"data": base64_image, "id": 0}]
            }
        else:
            # Default to Claude format if model is unknown
            logger.warning(f"Unknown vision model: {model_id}, defaulting to Claude format")
            prompt = f"""
            Human: <image>
            Please analyze this image in detail. If it's an architecture diagram, explain the components and their relationships. 
            If it's a screenshot containing an error, explain what the error is and suggest possible solutions.
            Provide a comprehensive analysis of what you see in the image.
            """
            
            payload = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 1500,
                "temperature": 0.2,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image", "source": {"type": "base64", "media_type": "image/jpeg", "data": base64_image}}
                        ]
                    }
                ]
            }
        
        # Invoke model
        response = bedrock_runtime.invoke_model(
            modelId=model_id,
            body=json.dumps(payload)
        )
        
        # Parse response
        response_body = json.loads(response['body'].read())
        
        # Extract text based on model type
        if 'anthropic' in model_id:
            analysis = response_body['content'][0]['text']
        elif 'meta' in model_id:
            analysis = response_body['generation']
        else:
            analysis = response_body['content'][0]['text']
        
        return {
            "status": "success",
            "analysis": analysis,
            "model_id": model_id
        }
    except Exception as e:
        logger.error(f"Image analysis with Bedrock failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "analysis": "Failed to analyze image with AI vision model."
        }

def summarize_image_with_bedrock(image_bytes: bytes) -> str:
    """
    Generate a concise summary of an image using AWS Bedrock.
    Args:
        image_bytes: Image file as bytes
    Returns:
        A string containing the image summary.
    """
    try:
        analysis_result = analyze_image_with_bedrock(image_bytes)
        if analysis_result.get("status") == "success":
            return analysis_result.get("analysis", "No summary available.")
        else:
            return f"Image analysis failed: {analysis_result.get('error', 'Unknown error')}"
    except Exception as e:
        logger.error(f"Error summarizing image with Bedrock: {e}")
        return "Error summarizing image."
