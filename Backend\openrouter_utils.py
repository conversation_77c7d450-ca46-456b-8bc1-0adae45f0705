# openrouter_utils.py - Integration with OpenRouter API for vision models
import os
import json
import base64
import requests
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

def analyze_image_with_openrouter(image_bytes: bytes, prompt: str = None) -> Dict[str, Any]:
    """
    Analyze image using OpenRouter API with Mistral Small 3.2 24B vision model.
    Args:
        image_bytes: Image file as bytes
        prompt: Optional custom prompt to use for analysis
    Returns:
        Dictionary containing analysis results
    """
    try:
        # Get API key from environment variable
        api_key = os.getenv('OPENROUTER_API_KEY')
        if not api_key:
            return {
                "status": "error",
                "error": "OPENROUTER_API_KEY environment variable not set",
                "analysis": "Failed to analyze image with OpenRouter API."
            }
        
        # Check image size (API may have limits)
        if len(image_bytes) > 20 * 1024 * 1024:  # 20MB limit
            return {
                "status": "error",
                "error": f"Image size ({len(image_bytes) / (1024 * 1024):.2f}MB) exceeds the 20MB limit",
                "analysis": "Image is too large for processing. Please use a smaller image."
            }
            
        # Try to resize the image if it's larger than 5MB (to avoid base64 encoding issues)
        if len(image_bytes) > 5 * 1024 * 1024:
            try:
                from PIL import Image
                from io import BytesIO
                
                # Open image and resize while maintaining aspect ratio
                with Image.open(BytesIO(image_bytes)) as img:
                    # Calculate new dimensions (maintaining aspect ratio)
                    max_size = 1600  # Maximum dimension in pixels
                    ratio = min(max_size / img.width, max_size / img.height)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    
                    # Resize image
                    img = img.resize(new_size, Image.LANCZOS)
                    
                    # Save to BytesIO
                    buffer = BytesIO()
                    img.save(buffer, format="JPEG", quality=85)
                    image_bytes = buffer.getvalue()
                    
                    logger.info(f"Resized image from {len(image_bytes) / (1024 * 1024):.2f}MB to {len(image_bytes) / (1024 * 1024):.2f}MB")
            except Exception as e:
                logger.warning(f"Failed to resize large image: {e}")
                # Continue with original image
        
        # Convert image to base64 if it's not already
        if isinstance(image_bytes, bytes):
            base64_image = base64.b64encode(image_bytes).decode('utf-8')
        else:
            base64_image = image_bytes
            
        # Default prompt if none provided
        if not prompt:
            prompt = "Please analyze this image in detail. If it's an architecture diagram, explain the components and their relationships. If it's a screenshot containing an error, explain what the error is and suggest possible solutions."
        
        # Determine which model to use
        # Try to get model from environment variable first
        model_id = os.getenv('OPENROUTER_MODEL_ID', None)
        
        # If no model is specified in env vars, use one of these models
        if not model_id:
            # List of models to try, in order of preference
            models_to_try = [
                "mistralai/mistral-small-3.2-24b-instruct:free",
                "anthropic/claude-3-haiku-20240307-v1:0",
                "google/gemini-pro-vision:free",
                "openai/gpt-4-vision-preview:free"
            ]
            
            # Use the first model in the list
            model_id = models_to_try[0]
            logger.info(f"Using OpenRouter model: {model_id}")
        
        # Prepare the request payload
        payload = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
        }
        
        # Set up headers
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": os.getenv('APP_URL', 'http://localhost:8000'),  # Optional
            "X-Title": "RAG Quadrant App"  # Optional
        }
        
        # Make the API request with improved error handling
        logger.info("Sending request to OpenRouter API...")
        response = requests.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            data=json.dumps(payload),
            timeout=90  # Increase timeout for image processing
        )
        
        # Log the response status and headers for debugging
        logger.info(f"OpenRouter API response status: {response.status_code}")
        logger.info(f"OpenRouter API response headers: {response.headers}")
        
        # Check if the request was successful
        if response.status_code == 200:
            try:
                response_data = response.json()
                logger.info(f"OpenRouter API response data keys: {response_data.keys()}")
                
                # Extract the analysis text from the response
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    if 'message' in response_data['choices'][0] and 'content' in response_data['choices'][0]['message']:
                        analysis = response_data['choices'][0]['message']['content']
                        model_id = response_data.get('model', 'mistralai/mistral-small-3.2-24b-instruct')
                        
                        return {
                            "status": "success",
                            "analysis": analysis,
                            "model_id": model_id
                        }
                    else:
                        logger.error(f"Unexpected response structure: {response_data['choices'][0]}")
                        return {
                            "status": "error",
                            "error": "Unexpected response structure from OpenRouter API",
                            "raw_response": str(response_data),
                            "analysis": "Failed to analyze image with OpenRouter API due to unexpected response format."
                        }
                else:
                    logger.error(f"No choices in response: {response_data}")
                    return {
                        "status": "error",
                        "error": "No content in response from OpenRouter API",
                        "raw_response": str(response_data),
                        "analysis": "Failed to analyze image with OpenRouter API."
                    }
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                return {
                    "status": "error",
                    "error": f"Failed to parse JSON response: {e}",
                    "raw_response": response.text[:1000],  # Include part of the raw response for debugging
                    "analysis": "Failed to analyze image with OpenRouter API due to invalid JSON response."
                }
        elif response.status_code == 429:
            return {
                "status": "error",
                "error": "Rate limit exceeded on OpenRouter API",
                "analysis": "The OpenRouter API rate limit has been exceeded. Please try again later."
            }
        elif response.status_code == 401:
            return {
                "status": "error",
                "error": "Authentication failed with OpenRouter API",
                "analysis": "The OpenRouter API key is invalid or has expired."
            }
        else:
            error_message = f"OpenRouter API request failed with status code {response.status_code}: {response.text}"
            logger.error(error_message)
            return {
                "status": "error",
                "error": error_message,
                "analysis": "Failed to analyze image with OpenRouter API."
            }
    except Exception as e:
        logger.error(f"Image analysis with OpenRouter failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "analysis": "Failed to analyze image with OpenRouter API."
        }