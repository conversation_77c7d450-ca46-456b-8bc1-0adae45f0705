["Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_analyze_architecture", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_analyze_architecture_with_error", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_analyze_component_relationships", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_architecture_pattern_to_dict", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_build_architecture_graph", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_classify_component_roles", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_generate_details", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_generate_recommendations", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_generate_summary", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_identify_architecture_patterns", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_identify_potential_issues", "Backend/test_architecture_analyzer.py::TestArchitectureDiagramAnalyzer::test_init", "Backend/test_architecture_integration.py::TestArchitectureIntegration::test_architecture_analysis_integration", "Backend/test_architecture_integration.py::TestArchitectureIntegration::test_component_role_classification", "Backend/test_architecture_integration.py::TestArchitectureIntegration::test_microservices_pattern_detection", "Backend/test_architecture_integration.py::TestArchitectureIntegration::test_relationship_analysis", "Backend/test_diagram_components.py::test_associate_text_with_components", "Backend/test_diagram_components.py::test_associate_text_with_connections", "Backend/test_diagram_components.py::test_detect_connections", "Backend/test_diagram_components.py::test_detect_shapes", "Backend/test_diagram_components.py::test_get_diagram_structure", "Backend/test_diagram_components.py::test_init", "Backend/test_diagram_components.py::test_integration", "Backend/test_diagram_components.py::test_point_to_line_distance", "Backend/test_diagram_components.py::test_recognize_components_image_error", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_associate_text_with_elements", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_combined_alignment", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_detect_diagram_elements", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_determine_alignment", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_diagram_element_initialization", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_diagram_element_to_dict", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_extract_labels", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_extract_text_elements", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_extract_text_with_positions", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_get_text_element_relationships", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_is_title", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_nonexistent_file", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_svg_file", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_text_element_initialization", "Backend/test_diagram_text.py::TestDiagramTextRecognitionService::test_text_element_to_dict", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_batch_analyze", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_delete_analysis", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_get_analysis_result", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_get_image", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_get_service_status", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_invalid_image_upload", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_nonexistent_analysis", "Backend/test_endpoints.py::TestImageAnalysisAPI::test_upload_image", "Backend/test_error_recognition.py::test_analyze_error_screenshot", "Backend/test_error_recognition.py::test_calculate_confidence", "Backend/test_error_recognition.py::test_classify_error_type", "Backend/test_error_recognition.py::test_detect_alert_dialogs", "Backend/test_error_recognition.py::test_detect_error_icons", "Backend/test_error_recognition.py::test_detect_red_regions", "Backend/test_error_recognition.py::test_detect_visual_indicators_image_error", "Backend/test_error_recognition.py::test_detect_yellow_regions", "Backend/test_error_recognition.py::test_determine_severity", "Backend/test_error_recognition.py::test_generate_error_description", "Backend/test_error_recognition.py::test_generate_solution", "Backend/test_error_recognition.py::test_identify_errors", "Backend/test_error_recognition.py::test_init", "Backend/test_error_recognition.py::test_integration", "Backend/test_error_recognition.py::test_severity_to_numeric", "Backend/test_error_recognition.py::test_suggest_solutions", "Backend/test_image_classifier.py::test_calculate_architecture_score", "Backend/test_image_classifier.py::test_calculate_color_diversity", "Backend/test_image_classifier.py::test_calculate_edge_density", "Backend/test_image_classifier.py::test_calculate_error_score", "Backend/test_image_classifier.py::test_calculate_keyword_presence", "Backend/test_image_classifier.py::test_calculate_text_density", "Backend/test_image_classifier.py::test_classify_image_with_text", "Backend/test_image_classifier.py::test_detect_shapes", "Backend/test_image_classifier.py::test_detect_ui_elements", "Backend/test_image_classifier.py::test_extract_features", "Backend/test_image_classifier.py::test_init", "Backend/test_image_classifier.py::test_integration_with_test_images", "Backend/test_image_storage.py::TestAnalysisResultStore::test_cleanup_old_results", "Backend/test_image_storage.py::TestAnalysisResultStore::test_delete_request_and_result", "Backend/test_image_storage.py::TestAnalysisResultStore::test_get_result_by_request", "Backend/test_image_storage.py::TestAnalysisResultStore::test_store_request", "Backend/test_image_storage.py::TestAnalysisResultStore::test_store_result", "Backend/test_image_storage.py::TestSecureFileHandler::test_encrypt_decrypt_file", "Backend/test_image_storage.py::TestSecureFileHandler::test_secure_token", "Backend/test_image_storage.py::TestTemporaryImageStore::test_cleanup_old_images", "Backend/test_image_storage.py::TestTemporaryImageStore::test_delete_image", "Backend/test_image_storage.py::TestTemporaryImageStore::test_secure_url_generation", "Backend/test_image_storage.py::TestTemporaryImageStore::test_store_image", "Backend/test_image_storage.py::TestTemporaryImageStore::test_store_image_secure", "Backend/test_image_validation.py::test_compute_file_hash", "Backend/test_image_validation.py::test_get_image_metadata", "Backend/test_image_validation.py::test_sanitize_filename", "Backend/test_image_validation.py::test_validate_file_size_invalid", "Backend/test_image_validation.py::test_validate_file_size_valid", "Backend/test_image_validation.py::test_validate_file_type_invalid", "Backend/test_image_validation.py::test_validate_file_type_valid", "Backend/test_image_validation.py::test_validate_image_all_checks", "Backend/test_image_validation.py::test_validate_image_content_invalid", "Backend/test_image_validation.py::test_validate_image_content_valid", "Backend/test_image_validation.py::test_validate_image_dimensions", "Backend/test_image_validation.py::test_validate_svg_content_invalid", "Backend/test_image_validation.py::test_validate_svg_content_valid", "Backend/test_logging.py::test_exception_logging", "Backend/test_logging.py::test_logging_levels", "Backend/test_logging.py::test_structured_logging", "Backend/test_ocr.py::TestOCRService::test_extract_tables", "Backend/test_ocr.py::TestOCRService::test_extract_text_basic", "Backend/test_ocr.py::TestOCRService::test_extract_text_with_different_languages", "Backend/test_ocr.py::TestOCRService::test_extract_text_with_different_modes", "Backend/test_ocr.py::TestOCRService::test_extract_text_with_layout", "Backend/test_ocr.py::TestOCRService::test_extract_text_with_preprocessing", "Backend/test_ocr.py::TestOCRService::test_nonexistent_file", "Backend/test_ocr.py::TestOCRService::test_svg_file", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_aggregate_results", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_analyze_content_architecture", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_analyze_content_error", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_classify_image", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_fallback_failure", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_fallback_success", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_step_with_retries_failure_and_fallback", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_step_with_retries_success", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_extract_features_architecture", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_extract_features_error", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_extract_text", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_analysis", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_classification", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_feature_extraction", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_text_extraction", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_generate_result", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_classification_error", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_feature_extraction_error", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_missing_image", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_missing_request", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_success", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_text_extraction_error", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_with_retries_success", "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_validate_request", "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_all_steps_fail", "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_missing_request", "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_step_failure_with_fallback", "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_step_failure_with_retry", "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_success", "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_missing_request", "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_success", "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_text_extraction_failure", "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_validation_failure", "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_with_retries_missing_request", "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_with_retries_success", "Backend/test_s3_connection.py::test_aws_client", "Backend/test_s3_connection.py::test_aws_credentials", "Backend/test_s3_connection.py::test_s3_bucket_access", "Backend/test_s3_connection.py::test_s3_file_operations", "Backend/test_vector_store.py::test_add_and_retrieve", "Backend/test_vector_store.py::test_embeddings", "Backend/test_vector_store.py::test_vector_search", "Backend/test_vector_store.py::test_vector_store_connection"]