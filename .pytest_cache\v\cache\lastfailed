{"Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_step_failure_with_retry": true, "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_step_failure_with_fallback": true, "Backend/test_orchestrator_implementation.py::TestAnalysisOrchestratorImplementation::test_process_request_with_retries_all_steps_fail": true, "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_text_extraction_failure": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_text_extraction_error": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_classification_error": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_feature_extraction_error": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_step_with_retries_failure_and_fallback": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_fallback_success": true, "Backend/test_multi_modal.py": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_success": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_missing_request": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_missing_image": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_validate_request": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_extract_text": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_classify_image": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_extract_features_architecture": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_extract_features_error": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_analyze_content_architecture": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_analyze_content_error": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_generate_result": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_process_request_with_retries_success": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_step_with_retries_success": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_execute_fallback_failure": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_text_extraction": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_classification": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_feature_extraction": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_fallback_analysis": true, "Backend/test_orchestrator.py::TestAnalysisOrchestrator::test_aggregate_results": true, "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_success": true, "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_missing_request": true, "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_validation_failure": true, "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_with_retries_success": true, "Backend/test_orchestrator_simple.py::TestAnalysisOrchestratorSimple::test_process_request_with_retries_missing_request": true, "Backend/test_query_advanced.py": true, "Backend/test_diagram_text.py::TestDiagramTextRecognitionService": true, "Backend/test_error_recognition.py::test_init": true, "Backend/test_error_recognition.py::test_identify_errors": true, "Backend/test_error_recognition.py::test_determine_severity": true, "Backend/test_error_recognition.py::test_calculate_confidence": true, "Backend/test_error_recognition.py::test_generate_error_description": true, "Backend/test_error_recognition.py::test_suggest_solutions": true, "Backend/test_error_recognition.py::test_severity_to_numeric": true, "Backend/test_error_recognition.py::test_generate_solution": true, "Backend/test_error_recognition.py::test_classify_error_type": true, "Backend/test_error_recognition.py::test_detect_visual_indicators_image_error": true, "Backend/test_error_recognition.py::test_detect_red_regions": true, "Backend/test_error_recognition.py::test_detect_yellow_regions": true, "Backend/test_error_recognition.py::test_detect_error_icons": true, "Backend/test_error_recognition.py::test_detect_alert_dialogs": true, "Backend/test_error_recognition.py::test_analyze_error_screenshot": true, "Backend/test_error_recognition.py::test_integration": true}